package backend

import (
	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/security"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/models/role"
	"github.com/MiaoSiLa/missevan-main/config"
	"github.com/MiaoSiLa/missevan-main/controllers/backend/recommended"
)

// HandlerV2 returns the registered handlerV2
func HandlerV2(conf *config.Config) handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "backend/x",
		Middlewares: gin.HandlersChain{
			security.CSRFMiddleware(conf.HTTP.CSRFAllowTopDomains...),
			user.Middleware(),
		},
		SubHandlers: []handler.HandlerV2{
			recommendedHandlerV2(),
		},
	}
}

func recommendedHandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "recommended",
		Middlewares: gin.HandlersChain{
			user.IsRole(role.OperationSeniorPlatformEdit, role.OperationSuperiorPlatformEdit),
		},
		Actions: map[string]*handler.ActionV2{
			// 旧版管理接口
			"home-feed/add":       handler.NewActionV2(handler.POST, recommended.ActionHomeFeedAdd, handler.ActionOption{LoginRequired: true}),
			"home-feed/batch-add": handler.NewActionV2(handler.POST, recommended.ActionHomeFeedBatchAdd, handler.ActionOption{LoginRequired: true}),
			"home-feed/update":    handler.NewActionV2(handler.POST, recommended.ActionHomeFeedUpdate, handler.ActionOption{LoginRequired: true}),
			"home-feed/delete":    handler.NewActionV2(handler.POST, recommended.ActionHomeFeedDelete, handler.ActionOption{LoginRequired: true}),

			// 剧集卡管理接口
			"add-drama":       handler.NewActionV2(handler.POST, recommended.ActionAddDrama, handler.ActionOption{LoginRequired: true}),
			"batch-add-drama": handler.NewActionV2(handler.POST, recommended.ActionBatchAddDrama, handler.ActionOption{LoginRequired: true}),
			"update-drama":    handler.NewActionV2(handler.POST, recommended.ActionUpdateDrama, handler.ActionOption{LoginRequired: true}),
			"delete-drama":    handler.NewActionV2(handler.POST, recommended.ActionDeleteDrama, handler.ActionOption{LoginRequired: true}),

			// 直播卡管理接口
			"add-live":       handler.NewActionV2(handler.POST, recommended.ActionAddLive, handler.ActionOption{LoginRequired: true}),
			"batch-add-live": handler.NewActionV2(handler.POST, recommended.ActionBatchAddLive, handler.ActionOption{LoginRequired: true}),
			"update-live":    handler.NewActionV2(handler.POST, recommended.ActionUpdateLive, handler.ActionOption{LoginRequired: true}),
			"delete-live":    handler.NewActionV2(handler.POST, recommended.ActionDeleteLive, handler.ActionOption{LoginRequired: true}),
		},
	}
}
