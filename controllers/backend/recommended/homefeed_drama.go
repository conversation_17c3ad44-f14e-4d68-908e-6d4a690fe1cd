package recommended

import (
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// AddDramaParams 添加剧集卡参数
type AddDramaParams struct {
	ElementID       int64 `json:"element_id" form:"element_id"`               // 剧集 ID
	ExposureLevelID int64 `json:"exposure_level_id" form:"exposure_level_id"` // 曝光等级 ID
	StartTime       int64 `json:"start_time" form:"start_time"`               // 开始生效时间戳，单位：秒
	EndTime         int64 `json:"end_time" form:"end_time"`                   // 结束生效时间戳，单位：秒
}

// ActionAddDrama 添加剧集卡
/**
 * @api {post} /backend/x/recommended/add-drama 添加剧集卡
 * @apiVersion 0.1.0
 * @apiGroup /backend/x/recommended
 * @apiPermission staff
 *
 * @apiParam {Number} element_id 剧集 ID
 * @apiParam {Number} exposure_level_id 曝光等级 ID
 * @apiParam {Number} start_time 开始生效时间戳，单位：秒
 * @apiParam {Number} end_time 结束生效时间戳，单位：秒
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "添加成功",
 *     "data": null
 *   }
 */
func ActionAddDrama(c *handler.Context) (handler.ActionResponse, string, error) {
	return nil, "", nil
}

// BatchAddDramaParams 批量添加剧集卡参数
type BatchAddDramaParams struct {
	ElementIDs      string `json:"element_ids" form:"element_ids"`             // 剧集 ID 列表，以英文逗号分隔，最多 1000 个
	ExposureLevelID int64  `json:"exposure_level_id" form:"exposure_level_id"` // 曝光等级 ID
	StartTime       int64  `json:"start_time" form:"start_time"`               // 开始生效时间戳，单位：秒
	EndTime         int64  `json:"end_time" form:"end_time"`                   // 结束生效时间戳，单位：秒
}

// ActionBatchAddDrama 批量添加剧集卡
/**
 * @api {post} /backend/x/recommended/batch-add-drama 批量添加剧集卡
 * @apiVersion 0.1.0
 * @apiGroup /backend/x/recommended
 * @apiPermission staff
 *
 * @apiParam {String} element_ids 剧集 ID 列表，以英文逗号分隔，最多 1000 个
 * @apiParam {Number} exposure_level_id 曝光等级 ID
 * @apiParam {Number} start_time 开始生效时间戳，单位：秒
 * @apiParam {Number} end_time 结束生效时间戳，单位：秒
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "批量创建成功",
 *     "data": null
 *   }
 *
 * @apiErrorExample Error-Response:
 *   {
 *     "code": 400,
 *     "message": "789: 不支持的剧集分类, 790: 剧集 ID 对应剧集不存在",
 *     "data": null
 *   }
 */
func ActionBatchAddDrama(c *handler.Context) (handler.ActionResponse, string, error) {
	return nil, "", nil
}

// UpdateDramaParams 修改剧集卡参数
type UpdateDramaParams struct {
	ID              int64 `json:"id" form:"id"`                               // 卡片 ID
	ElementID       int64 `json:"element_id" form:"element_id"`               // 剧集 ID
	ExposureLevelID int64 `json:"exposure_level_id" form:"exposure_level_id"` // 曝光等级 ID
	StartTime       int64 `json:"start_time" form:"start_time"`               // 开始生效时间戳，单位：秒
	EndTime         int64 `json:"end_time" form:"end_time"`                   // 结束生效时间戳，单位：秒
}

// ActionUpdateDrama 修改剧集卡
/**
 * @api {post} /backend/x/recommended/update-drama 修改剧集卡
 * @apiVersion 0.1.0
 * @apiGroup /backend/x/recommended
 * @apiPermission staff
 *
 * @apiParam {Number} id 卡片 ID
 * @apiParam {Number} element_id 剧集 ID
 * @apiParam {Number} exposure_level_id 曝光等级 ID
 * @apiParam {Number} start_time 开始生效时间戳，单位：秒
 * @apiParam {Number} end_time 结束生效时间戳，单位：秒
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "更新成功",
 *     "data": null
 *   }
 */
func ActionUpdateDrama(c *handler.Context) (handler.ActionResponse, string, error) {
	return nil, "", nil
}

// DeleteDramaParams 删除剧集卡参数
type DeleteDramaParams struct {
	ID int64 `json:"id" form:"id"` // 卡片 ID
}

// ActionDeleteDrama 删除剧集卡
/**
 * @api {post} /backend/x/recommended/delete-drama 删除剧集卡
 * @apiVersion 0.1.0
 * @apiGroup /backend/x/recommended
 * @apiPermission staff
 *
 * @apiParam {Number} id 卡片 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "删除成功",
 *     "data": null
 *   }
 */
func ActionDeleteDrama(c *handler.Context) (handler.ActionResponse, string, error) {
	return nil, "", nil
}
