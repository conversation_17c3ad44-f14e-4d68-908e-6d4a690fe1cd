package recommended

import (
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// AddLiveParams 添加直播卡参数
type AddLiveParams struct {
	ElementID       int64 `json:"element_id" form:"element_id"`               // 主播 ID
	ExposureLevelID int64 `json:"exposure_level_id" form:"exposure_level_id"` // 曝光等级 ID
	StartTime       int64 `json:"start_time" form:"start_time"`               // 开始生效时间戳，单位：秒
	EndTime         int64 `json:"end_time" form:"end_time"`                   // 结束生效时间戳，单位：秒
}

// ActionAddLive 添加直播卡
/**
 * @api {post} /backend/x/recommended/add-live 添加直播卡
 * @apiVersion 0.1.0
 * @apiGroup /backend/x/recommended
 * @apiPermission staff
 *
 * @apiParam {Number} element_id 主播 ID
 * @apiParam {Number} exposure_level_id 曝光等级 ID
 * @apiParam {Number} start_time 开始生效时间戳，单位：秒
 * @apiParam {Number} end_time 结束生效时间戳，单位：秒
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "添加成功",
 *     "data": null
 *   }
 */
func ActionAddLive(c *handler.Context) (handler.ActionResponse, string, error) {
	return nil, "", nil
}

// BatchAddLiveParams 批量添加直播卡参数
type BatchAddLiveParams struct {
	ElementIDs      string `json:"element_ids" form:"element_ids"`             // 主播 ID 列表，以英文逗号分隔，最多 1000 个
	ExposureLevelID int64  `json:"exposure_level_id" form:"exposure_level_id"` // 曝光等级 ID
	StartTime       int64  `json:"start_time" form:"start_time"`               // 开始生效时间戳，单位：秒
	EndTime         int64  `json:"end_time" form:"end_time"`                   // 结束生效时间戳，单位：秒
}

// ActionBatchAddLive 批量添加直播卡
/**
 * @api {post} /backend/x/recommended/batch-add-live 批量添加直播卡
 * @apiVersion 0.1.0
 * @apiGroup /backend/x/recommended
 * @apiPermission staff
 *
 * @apiParam {String} element_ids 主播 ID 列表，以英文逗号分隔，最多 1000 个
 * @apiParam {Number} exposure_level_id 曝光等级 ID
 * @apiParam {Number} start_time 开始生效时间戳，单位：秒
 * @apiParam {Number} end_time 结束生效时间戳，单位：秒
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "批量创建成功",
 *     "data": null
 *   }
 *
 * @apiErrorExample Error-Response:
 *   {
 *     "code": 400,
 *     "message": "789: 主播已注销, 790: 直播间不存在",
 *     "data": null
 *   }
 */
func ActionBatchAddLive(c *handler.Context) (handler.ActionResponse, string, error) {
	return nil, "", nil
}

// UpdateLiveParams 修改直播卡参数
type UpdateLiveParams struct {
	ID              int64 `json:"id" form:"id"`                               // 卡片 ID
	ElementID       int64 `json:"element_id" form:"element_id"`               // 主播 ID
	ExposureLevelID int64 `json:"exposure_level_id" form:"exposure_level_id"` // 曝光等级 ID
	StartTime       int64 `json:"start_time" form:"start_time"`               // 开始生效时间戳，单位：秒
	EndTime         int64 `json:"end_time" form:"end_time"`                   // 结束生效时间戳，单位：秒
}

// ActionUpdateLive 修改直播卡
/**
 * @api {post} /backend/x/recommended/update-live 修改直播卡
 * @apiVersion 0.1.0
 * @apiGroup /backend/x/recommended
 * @apiPermission staff
 *
 * @apiParam {Number} id 卡片 ID
 * @apiParam {Number} element_id 主播 ID
 * @apiParam {Number} exposure_level_id 曝光等级 ID
 * @apiParam {Number} start_time 开始生效时间戳，单位：秒
 * @apiParam {Number} end_time 结束生效时间戳，单位：秒
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "更新成功",
 *     "data": null
 *   }
 */
func ActionUpdateLive(c *handler.Context) (handler.ActionResponse, string, error) {
	return nil, "", nil
}

// DeleteLiveParams 删除直播卡参数
type DeleteLiveParams struct {
	ID int64 `json:"id" form:"id"` // 卡片 ID
}

// ActionDeleteLive 删除直播卡
/**
 * @api {post} /backend/x/recommended/delete-live 删除直播卡
 * @apiVersion 0.1.0
 * @apiGroup /backend/x/recommended
 * @apiPermission staff
 *
 * @apiParam {Number} id 卡片 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "删除成功",
 *     "data": null
 *   }
 */
func ActionDeleteLive(c *handler.Context) (handler.ActionResponse, string, error) {
	return nil, "", nil
}
