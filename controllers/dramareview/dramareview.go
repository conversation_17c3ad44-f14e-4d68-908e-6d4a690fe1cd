package dramareview

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
)

// Handler 返回 dramareview handler
func Handler() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "drama/review",
		Actions: map[string]*handler.ActionV2{
			"check-auth":        handler.NewActionV2(handler.POST, ActionCheckAuth, handler.ActionOption{LoginRequired: true}),
			"auth":              handler.NewActionV2(handler.POST, ActionAuth, handler.ActionOption{LoginRequired: true}),
			"sendcode":          handler.NewActionV2(handler.POST, ActionSendcode, handler.ActionOption{LoginRequired: true}),
			"check-sign-status": handler.NewActionV2(handler.POST, ActionCheckSignStatus, handler.ActionOption{LoginRequired: true}),
		},
		SubHandlers: []handler.HandlerV2{
			{
				Middlewares: gin.HandlersChain{
					middlewareCheckAuth(),
				},
				Actions: map[string]*handler.ActionV2{
					"flow/overview":     handler.NewActionV2(handler.GET, ActionFlowOverview, handler.ActionOption{LoginRequired: true}),
					"flow/daily":        handler.NewActionV2(handler.GET, ActionDailyFlow, handler.ActionOption{LoginRequired: true}),
					"flow/monthly":      handler.NewActionV2(handler.GET, ActionMonthlyFlow, handler.ActionOption{LoginRequired: true}),
					"flow/daily-v2":     handler.NewActionV2(handler.GET, ActionDailyFlowV2, handler.ActionOption{LoginRequired: true}),
					"flow/monthly-v2":   handler.NewActionV2(handler.GET, ActionMonthlyFlowV2, handler.ActionOption{LoginRequired: true}),
					"flow/daily-export": handler.NewActionV2(handler.GET, ActionDailyFlowExport, handler.ActionOption{LoginRequired: true}),
				},
			},
		},
	}
}

func middlewareCheckAuth() func(c *gin.Context) {
	return func(c *gin.Context) {
		u, err := user.GetUser(c)
		if err != nil {
			abortWithInternalServerError(c, err)
			return
		}
		if u == nil {
			err := handler.ErrLoginRequired
			c.AbortWithStatusJSON(err.StatusCode(), handler.BasicResponseV2{Code: err.ErrorCode(), Message: handler.ErrLoginRequired.Error(), Data: nil})
			return
		}

		token, _ := c.Cookie("token")
		data, err := checkAuth(u.ID, token)
		if err != nil {
			var v handler.ResponseError
			switch {
			case errors.As(err, &v):
				c.AbortWithStatusJSON(http.StatusForbidden, handler.BasicResponseV2{Code: v.ErrorCode(), Message: v.ErrorMessage(), Data: data})
				return
			default:
				abortWithInternalServerError(c, err)
				return
			}
		}

		c.Next()
	}
}

func abortWithInternalServerError(c *gin.Context, err error) {
	logger.Errorf("%s: %v", c.Request.URL.String(), err)
	c.AbortWithStatusJSON(http.StatusInternalServerError, handler.BasicResponseV2{Code: 100010500, Message: "服务器内部错误"})
}
