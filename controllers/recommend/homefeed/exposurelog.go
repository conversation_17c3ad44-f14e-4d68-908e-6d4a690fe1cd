package homefeed

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/tianma"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/mrecommendedelementsdailyexposure"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/keys"
)

const (
	recommendAttrTianma     = iota + 1 // 天马推荐算法下发
	recommendAttrSupplement            // 天马推荐算法下发不足时进行补位
	recommendAttrOpConfig              // 运营干预卡
	recommendAttrManual                // 人工配置
)

type exposureLog struct {
	UserID      int64             `json:"user_id"`      // 用户 ID
	OS          int               `json:"os"`           // 操作系统
	EquipID     string            `json:"equip_id"`     // 设备 ID
	Buvid       string            `json:"buvid"`        // 设备唯一标识
	Channel     string            `json:"channel"`      // 渠道
	AppVersion  string            `json:"app_version"`  // App 版本
	UserAgent   string            `json:"user_agent"`   // 用户代理
	IP          string            `json:"ip"`           // IP 地址
	Network     int               `json:"network"`      // 网络类型
	RefreshType tianma.FreshType  `json:"refresh_type"` // 刷新类型
	RefreshNum  int64             `json:"refresh_num"`  // 刷新次数
	CreateTime  int64             `json:"create_time"`  // 创建时间
	TrackID     string            `json:"track_id"`     // 算法跟踪 ID
	UserFeature string            `json:"user_feature"` // 用户特征
	Env         string            `json:"env"`          // 环境
	Scene       string            `json:"scene"`        // 场景
	ShowList    []exposureLogItem `json:"showlist"`     // 曝光列表
}

type exposureLogItem struct {
	ID        int64       `json:"id"`                   // 推荐项 ID
	Goto      tianma.Goto `json:"goto"`                 // 跳转类型
	Pos       int         `json:"pos"`                  // 曝光位置
	Source    string      `json:"source,omitempty"`     // 来源
	AvFeature string      `json:"av_feature,omitempty"` // 卡片特征
	Attr      int         `json:"attr"`                 // 资源位来源
}

type exposureLogSender struct {
	Context     *handler.Context // 请求上下文
	Cards       []*feedCard      // 推荐卡片
	Network     int              // 网络类型
	RefreshType tianma.FreshType // 刷新类型
	RefreshNum  int64            // 刷新次数
	UserFeature string           // 用户特征
}

func (p exposureLogSender) send() {
	key := p.buildKey()
	log := p.buildLogData()

	var operateIDs []int64
	for _, card := range p.Cards {
		if card.Attr == recommendAttrOpConfig && card.AvFeature != "" {
			var avf avFeature
			if err := json.Unmarshal([]byte(card.AvFeature), &avf); err == nil && avf.OperateID != 0 {
				operateIDs = append(operateIDs, avf.OperateID)
			}
		}
	}
	mrecommendedelementsdailyexposure.UpdateOrCreateDailyExposure(operateIDs, mrecommendedelementsdailyexposure.SceneHomePage)

	goutil.Go(func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		err := service.Databus.AppLogPub.Send(ctx, key, log)
		if err != nil {
			logger.Errorf("曝光日志发送失败: %v", err)
			// PASS
		}
	})
}

const sceneMaoerFeeds = "maoer_feeds" // 首页 Feed 推荐

func (p exposureLogSender) buildKey() string {
	var keySuffix string

	if p.Context.UserID() != 0 {
		keySuffix = strconv.FormatInt(p.Context.UserID(), 10)
	} else {
		keySuffix = fmt.Sprintf(":%08x", goutil.CRC32(p.Context.ClientIP()))
	}

	return keys.DatabusKeyRecomendExposureLog1.Format(keySuffix)
}

func (p exposureLogSender) buildLogData() *exposureLog {
	equipment := p.Context.Equip()
	channel := p.Context.Request().Header.Get("channel")

	var trackID string
	if len(p.Cards) > 0 {
		trackID = p.Cards[0].TrackID
	}

	log := &exposureLog{
		UserID:      p.Context.UserID(),
		OS:          int(equipment.OS),
		EquipID:     equipment.EquipID,
		Buvid:       equipment.BUVID,
		Channel:     channel,
		AppVersion:  equipment.AppVersion,
		UserAgent:   p.Context.UserAgent(),
		IP:          p.Context.ClientIP(),
		Network:     p.Network,
		RefreshType: p.RefreshType,
		RefreshNum:  p.RefreshNum,
		CreateTime:  goutil.TimeNow().Unix(),
		TrackID:     trackID,
		UserFeature: p.UserFeature,
		Env:         os.Getenv("DEPLOY_ENV"),
		Scene:       sceneMaoerFeeds,
		ShowList:    []exposureLogItem{},
	}

	showList := buildExposureItemsFromCards(p.Cards)
	log.ShowList = showList

	return log
}

func buildExposureItemsFromCards(cards []*feedCard) []exposureLogItem {
	items := make([]exposureLogItem, 0, len(cards))

	for i, card := range cards {
		items = append(items, exposureLogItem{
			ID:        card.ID,
			Goto:      card.Goto,
			Pos:       i + 1, // 位置从 1 开始
			Source:    card.Source,
			AvFeature: card.AvFeature,
			Attr:      card.Attr,
		})
	}

	return items
}
