package homefeed

import (
	"github.com/MiaoSiLa/missevan-go/service/bilibili/tianma"
)

type cardType int

const (
	cardTypeDrama        cardType = 1 // 剧集卡
	cardTypeLive         cardType = 2 // 直播卡
	cardTypeExtraBanner  cardType = 3 // 副通栏异形卡
	cardTypeRanks        cardType = 4 // 排行榜异形卡
	cardTypeLiveNow      cardType = 5 // 正在直播异形卡
	cardTypeCustomModule cardType = 6 // 自定义模块异形卡
)

type elementType int

const (
	elementTypeDrama elementType = 2 // 剧集
	elementTypeSound elementType = 3 // 音频
	elementTypeLive  elementType = 5 // 直播
)

type feedCard struct {
	CardType       cardType        `json:"card_type"`          // 卡片类型
	CardData       any             `json:"card_data"`          // 卡片数据
	DislikeReasons []dislikeReason `json:"-,omitempty"`        // 不感兴趣原因，TODO: 等实现 dislike 功能后再下发
	Trace          string          `json:"trace,omitempty"`    // 埋点数据
	TrackID        string          `json:"track_id,omitempty"` // 埋点 ID

	// 埋点字段，不在 API 响应中返回
	ID                int64       `json:"-"` // 推荐项 ID
	Goto              tianma.Goto `json:"-"` // 跳转类型
	Pos               int         `json:"-"` // 曝光位置
	Source            string      `json:"-"` // 来源
	AvFeature         string      `json:"-"` // 卡片特征
	UserFeature       string      `json:"-"` // 用户特征
	Attr              int         `json:"-"` // 资源位来源
	Labels            []string    `json:"-"` // 标签数组，格式为 ["label_type:label_id:label_name"]
	CornerMark        string      `json:"-"` // 剧集卡片的角标名
	PreviewIntroTitle string      `json:"-"` // 直播卡片的补充信息文案
}

type avFeature struct {
	OperateID int64 `json:"operate_id,omitempty"`
}

type dislikeReason struct {
	Text   string `json:"text"`   // 不感兴趣原因文案
	Reason string `json:"reason"` // 不感兴趣原因，JSON 字符串，点击后通过 POST 请求上报
}
