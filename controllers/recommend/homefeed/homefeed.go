package homefeed

import (
	"encoding/json"
	"fmt"
	"sort"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/tianma"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/models/mpersonamoduleelement"
	"github.com/MiaoSiLa/missevan-main/models/persona"
	"github.com/MiaoSiLa/missevan-main/models/user"
	"github.com/MiaoSiLa/missevan-main/service"
	"github.com/MiaoSiLa/missevan-main/service/appapi"
	"github.com/MiaoSiLa/missevan-main/service/keys"
	"github.com/MiaoSiLa/missevan-main/service/liveapi"
)

// ActionHomeFeed 获取首页 Feed 流
/**
 * @api {get} /x/recommend/home-feed 获取首页 Feed 流
 *
 * @apiVersion 0.1.0
 * @apiName home-feed
 * @apiGroup /x/recommend
 *
 * @apiParam {Number} persona_id 画像 ID
 * @apiParam {number=0,1,2,3,4} refresh_type 刷新类型，0: 默认值，1: 自动刷新，2: 顶部下拉，3: 点击换一批，4: 底部上滑
 * @apiParam {Number} feed_type 首页 Feed 流类型，不同枚举值代表不同实验组，0 为对照组
 * @apiParam {number=0,1,2,3,4} [network=0] 用户当前网络状况 （同埋点上报参数约定值，即：0: UNKNOWN; 1: WIFI; 2: CELLULAR; 3: OFFLINE; 4: OTHERNET）
 * @apiParam {String} [reset_marker] 从头开始加载列表时，传入当前列表的 marker 值，冷启动后第一次请求时不传，加载更多时不传
 * @apiParam {String} [marker] 加载更多标记，从头开始加载列表时不传，加载更多时为上一次请求返回的 marker 值
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "cards": [
 *         {
 *           "card_type": 1, // 卡片类型，1：剧集卡，2：直播卡，3：副通栏异形卡，4：排行榜异形卡，5：正在直播异形卡，6：自定义模块异形卡
 *           "card_data": {
 *             "element_type": 2, // 元素类型，用于点击事件埋点上报，1：音单，2：剧集，3：音频，5：直播
 *             "drama_id": 9888, // 剧集 ID
 *             "sound_id": 9888, // 音频 ID
 *             "title": "《杀破狼》广播剧", // 剧集标题
 *             "cover_url": "https://test.com/test.jpg", // 剧集封面
 *             "cover_color": 5402961, // 背景图主颜色，十进制表示
 *             "intro": "晋江文学城", // 剧集简介，优先下发运营配置的一行简介
 *             "ipr_type": 1, // 版权类型，1：精品版权，2：非精品版权
 *             "cvs": ["顾昀", "沈易", "长庚", "李丰"], // 声优列表，只下发主役声优
 *             "view_count": 12345, // 播放量
 *             "pay_type": 2, // 付费类型（0: 免费；1: 单集付费；2: 整剧付费）
 *             "integrity": 1, // 完结度（1: 长篇未完结；2: 长篇完结；3: 全一期）
 *             "newest": "第四期", // 最近更新
 *             "tags": [
 *               {
 *                 "style": 1, // 标签样式，1：粉底红字，2：灰底黑字
 *                 "text": "打赏总榜 No.1", // 标签文本
 *                 "url": "missevan://reward/rank?type=drama&tab=1" // 标签跳转链接，没有跳转链接时不返回该字段
 *               },
 *               {
 *                 "style": 2,
 *                 "text": "纯爱"
 *               }
 *             ],
 *             // 剧集无角标时不返回该字段
 *             "corner_mark": {
 *               "text": "已购", // 角标文本
 *               "text_color": "#FF0000", // 角标文本颜色
 *               "text_start_color": "#FF0000", // 角标文本渐变开始颜色，优先于 text_color
 *               "text_end_color": "#FF0000", // 角标文本渐变结束颜色，优先于 text_color
 *               "bg_start_color": "#FF0000", // 角标渐变开始颜色
 *               "bg_end_color": "#FF0000", // 角标渐变结束颜色
 *               "left_icon_url": "https://test.com/left_icon.png" // 无左侧图标时不返回该字段
 *             }
 *           },
 *           "dislike_reasons": [
 *             {
 *               "text": "重复推荐", // 不感兴趣原因文案
 *               "reason": "{\"type\":1,\"drama_id\":1}" // 不感兴趣原因，JSON 字符串，点击后通过 POST 请求上报
 *             },
 *             {
 *               "text": "不喜欢类型：言情", // 不感兴趣原因文案
 *               "reason": "{\"type\":2,\"tag_id\":1}" // 不感兴趣原因，JSON 字符串，点击后通过 POST 请求上报
 *             }
 *           ],
 *           "trace": "{ \"refresh_num\": 1, \"refresh_type\": 1 }", // 埋点数据
 *           "track_id": "" // 埋点 ID
 *         },
 *         {
 *           "card_type": 2, // 卡片类型，1：剧集卡，2：直播卡，3：副通栏异形卡，4：排行榜异形卡，5：正在直播异形卡，6：自定义模块异形卡
 *           "card_data": {
 *             "element_type": 5, // 元素类型，用于点击事件埋点上报，1：音单，2：剧集，3：音频，5：直播
 *             "room_id": 9888, // 直播间 ID
 *             "name": "《我在无限游戏封神》陪听", // 直播间名称
 *             "creator_id": 9888, // 主播 ID
 *             "creator_username": "主播", // 主播昵称
 *             "creator_iconurl": "https://test.com/avatar.jpg", // 主播头像
 *             "cover_url": "https://test.com/test.jpg", // 直播间封面
 *             "announcement": "欢迎来到我的直播间！", // 直播间公告
 *             "statistics": {
 *               "score": 9999 // 直播间热度
 *             },
 *             // 没有补充信息时，不返回该字段
 *             "extra_info": {
 *               "icon_url": "https://test.com/pk.png", // 补充信息图标，福袋、红包、PK 等
 *               "text": "主播正在连麦 PK" // 补充信息文本
 *             },
 *             "tags": [
 *               {
 *                 "style": 1, // 标签样式，1：粉底红字，2：灰底黑字
 *                 "text": "最近看过" // 标签文本
 *               },
 *               {
 *                 "style": 2,
 *                 "text": "音乐"
 *               }
 *             ]
 *           },
 *           "dislike_reasons": [
 *             {
 *               "text": "直播推荐过多", // 不感兴趣原因文案
 *               "reason": "{\"type\":3}" // 不感兴趣原因，JSON 字符串，点击后通过 POST 请求上报
 *             },
 *             {
 *               "text": "不喜欢该主播", // 不感兴趣原因文案
 *               "reason": "{\"type\":4,\"room_id\":9888}" // 不感兴趣原因，JSON 字符串，点击后通过 POST 请求上报
 *             }
 *           ],
 *           "trace": "{ \"refresh_num\": 1, \"refresh_type\": 1 }", // 埋点数据
 *           "track_id": "" // 埋点 ID
 *         },
 *         {
 *           "card_type": 3, // 卡片类型，1：剧集卡，2：直播卡，3：副通栏异形卡，4：排行榜异形卡，5：正在直播异形卡，6：自定义模块异形卡
 *           "card_data": {
 *             "module_id": 1, // 模块 ID
 *             "extra_banner": [
 *               {
 *                 "url": "http://www.missevan/voice/1", // 跳转链接
 *                 "pic": "http://static.missevan.com/dramacoversmini/201604dd9566b6083011.jpg" // 图片链接
 *               },
 *               {
 *                 "url": "http://www.missevan/voice/2", // 跳转链接
 *                 "pic": "http://static.missevan.com/dramacoversmini/201604dd9566b6083011.jpg" // 图片链接
 *               }
 *             ]
 *           },
 *         },
 *         {
 *           "card_type": 4, // 卡片类型，1：剧集卡，2：直播卡，3：副通栏异形卡，4：排行榜异形卡，5：正在直播异形卡，6：自定义模块异形卡
 *           "card_data": {
 *             "title": "排行榜", // 模块标题
 *             // 没有 more 字段的时候不显示更多按钮
 *             "more": {
 *               "url": "https://test.com/aaa?type=1" // 更多跳转链接
 *             },
 *             "data": [
 *               {
 *                 "type": 1, // 榜单类型（1: 新品榜；2: 人气榜；3: 打赏榜；4: 免费榜；5: 言情榜；6: 声音恋人榜；7：直播榜）
 *                 "name": "新品榜", // 榜单名称
 *                 "active": 1, // 默认定位到该榜单，不默认定位到该榜单时不下发
 *                 "open_url": "https://test.com/aaa?type=1", // 跳转链接
 *                 "element_type": 2, // 榜单内容类型（2: 剧集；3: 音频；5: 直播）
 *                 "elements": [
 *                   // 榜单数据前三个
 *                   {
 *                     "id": 9888, // 剧集/音频/直播 ID
 *                     "name": "《杀破狼》广播剧", // 标题
 *                     "cover": "https://test.com/test.jpg", // 封面
 *                     "abstract": "729声工场出品", // 简介
 *                     "cover_color": 5402961, // 背景图主颜色，十进制表示
 *                     "pay_type": 2, // 付费类型（0: 免费；1: 单集付费；2: 整剧付费）
 *                     "view_count": 12345, // 播放量
 *                     "integrity": 1, // 完结度（1: 长篇未完结；2: 长篇完结；3: 全一期）
 *                     "newest": "第四期", // 最近更新
 *                     // 无角标时不返回该字段
 *                     "corner_mark": {
 *                       "text": "已购",
 *                       "text_color": "#ffffff",
 *                       "bg_start_color": "#e66465",
 *                       "bg_end_color": "#e66465",
 *                       "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"
 *                     }
 *                   }
 *                 ]
 *               },
 *               {
 *                 "type": 6, // 榜单类型（6: 声音恋人榜）
 *                 "name": "声音恋人榜", // 榜单名称
 *                 "open_url": "https://test.com/aaa?type=6", // 跳转链接（跳转到榜单详情页，定位到相应的榜单的第一个子类型榜单）
 *                 "element_type": 3, // 榜单内容类型（2: 剧集；3: 音频；5: 直播）
 *                 "elements": [
 *                   // 榜单数据前三个
 *                   {
 *                     "id": 1462573, // 音频 ID
 *                     "duration": 21326, // 音频时长（单位：毫秒）
 *                     "soundstr": "迪奥光宗登场铃声", // 音频标题
 *                     "view_count": 12345, // 播放量
 *                     "front_cover": "https://test.com/test.jpg", // 音频封面
 *                     "pay_type": 1, // 付费类型（0: 免费；1: 单集付费；2: 整剧付费）
 *                     "user_id": 4983837, // UP 主用户 ID
 *                     "username": "扶暖文化", // UP 主用户名称
 *                     "iconurl": "https://test.com/profile/icon01.png", // UP 主头像
 *                     "video": true // 音频是否包含对应的视频
 *                   }
 *                 ]
 *               },
 *               {
 *                 "type": 7, // 榜单类型（7：直播榜）
 *                 "name": "直播榜",
 *                 "open_url": "https://test.com/aaa?type=7", // 跳转链接（跳转到榜单详情页，定位到直播榜）
 *                 "element_type": 5, // 榜单内容类型（2: 剧集；3: 音频；5: 直播）
 *                 "elements": [
 *                   // 榜单数据前三个
 *                   {
 *                     "user_id": 9467681, // 用户 ID
 *                     "username": "2Fire", // 用户名称
 *                     "iconurl": "https://test.com/profile/icon01.png", // 用户头像
 *                     "revenue": 60927, // 收益（分数值）
 *                     "rank_up": 10131, // 前一名的收益 - 自己的收益 + 1
 *                     "room": {
 *                       // 直播间信息
 *                       "room_id": 868858629,
 *                       "catalog_id": 145,
 *                       "name": "听歌进",
 *                       "announcement": "欢迎来到我的直播间！",
 *                       "creator_id": 9467681,
 *                       "creator_username": "2Fire",
 *                       "status": {
 *                         "open": 1
 *                       },
 *                       "cover_url": "https://test.com/fmcovers/test.jpg" // 直播间封面
 *                     }
 *                   }
 *                 ]
 *               }
 *             ]
 *           },
 *         },
 *         {
 *           "card_type": 5, // 卡片类型，1：剧集卡，2：直播卡，3：副通栏异形卡，4：排行榜异形卡，5：正在直播异形卡，6：自定义模块异形卡
 *           "card_data": [
 *             {
 *               "position": 1, // 该字段用作后台设置，这里请忽略
 *               "room_id": 22489473, // 房间号
 *               "name": "23333333", // 直播标题
 *               "cover_url": "https://static.missevan.com/profile/01.png", // 直播封面图
 *               "creator_id": 10,
 *               "creator_username": "bless01", // 主播昵称
 *               "creator_iconurl": "https://static.missevan.com/profile/01.png", // 主播头像
 *               "catalog_id": 10, // 分区 ID
 *               "catalog_name": "娱乐", // 分区名称
 *               "catalog_color": "#D68DFE", // 分区颜色
 *               "custom_tag": {
 *                 // 个性词条
 *                 "tag_id": 10001, // 个性词条 ID
 *                 "tag_name": "腹黑青叔" // 个性词条名称
 *               },
 *               "status": {
 *                 "open": 1, // 直播间是否开播，1：开启；0：关闭
 *                 "pk": 1, // 1：直播间在 PK 状态，0 或不存在：直播间不在 PK 状态
 *                 "red_packet": 1 // 当前待抢红包或可抢红包数量，0 或不存在表示无待抢红包或者可抢红包
 *               }
 *             }
 *             // ...
 *           ]
 *         },
 *         {
 *           "card_type": 6, // 卡片类型，1：剧集卡，2：直播卡，3：副通栏异形卡，4：排行榜异形卡，5：正在直播异形卡，6：自定义模块异形卡
 *           "card_data": {
 *             "module_id": 9, // 模块 ID
 *             "title": "VIP 精选", // 模块标题
 *             "type": 3, // 模块类型，1: 音单；2: 剧集；3: 音频
 *             "style": 4, // 排版方式，0：竖版；1：横版；2：排行榜；3：滑动；4：横版滑动
 *             // 没有 more 字段的时候不显示更多按钮
 *             "more": {
 *               "url": "https://test.com/aaa?foo=bar" // 更多按钮跳转链接
 *             },
 *             "elements": [
 *               // 当 type 为 1 时，elements 为音单列表
 *               {
 *                 "id": 8072438, // 音单 ID
 *                 "title": "音单名称", // 音单标题
 *                 "front_cover": "http://static-test.maoercdn.com/coversmini/201906/10/test.jpg", // 音单封面
 *                 "intro": "这是一句话简介", // 音单简介
 *                 "view_count": 233, // 播放量
 *                 "music_count": 10, // 音频数量
 *                 "sort": 0, // 排序
 *                 "module_id": 9 // 模块 ID
 *               },
 *               // 当 type 为 2 时，elements 为剧集列表
 *               {
 *                 "id": 81979, // 剧集 ID
 *                 "name": "剧集名称", // 剧集标题
 *                 "abstract": "这是一句话简介", // 剧集简介
 *                 "cover_color": 5402961, // 背景图主颜色，十进制表示
 *                 "integrity": 1, // 完结度 1：长篇未完结；2：长篇完结；3：全一期；4：微小剧
 *                 "newest": "第四期", // 最近更新
 *                 "pay_type": 2, // 付费类型，0：免费；1：单集付费；2：整剧付费
 *                 "view_count": 233, // 播放量
 *                 "outline": null, // 剧集设定
 *                 "need_pay": 1, // 付费状态，0：免费；1：付费剧集未付费；2：付费剧集已付费
 *                 "sort": 0, // 排序
 *                 "module_id": 9, // 模块 ID
 *                 "front_cover": "http://static-test.maoercdn.com/coversmini/201906/10/test.jpg", // 剧集封面
 *                 // 剧集无角标时不返回该字段
 *                 "corner_mark": {
 *                   "text": "已购", // 角标文本
 *                   "text_color": "#FF0000", // 角标文本颜色
 *                   "text_start_color": "#FF0000", // 角标文本渐变开始颜色，优先于 text_color
 *                   "text_end_color": "#FF0000", // 角标文本渐变结束颜色，优先于 text_color
 *                   "bg_start_color": "#FF0000", // 角标渐变开始颜色
 *                   "bg_end_color": "#FF0000", // 角标渐变结束颜色
 *                   "left_icon_url": "https://test.com/left_icon.png" // 无左侧图标时不返回该字段
 *                 }
 *               },
 *               // 当 type 为 3 时，elements 为音频列表
 *               {
 *                 "all_comments": 233, // 总评论数量
 *                 "comment_count": 0, // 弹幕数量
 *                 "front_cover": "http://static-test.maoercdn.com/coversmini/201906/10/test.jpg", // 音频封面
 *                 "id": 5621487, // 音频 ID
 *                 "intro": "这是一句话简介", // 音频简介
 *                 "module_id": 9, // 模块 ID
 *                 "sort": 1, // 排序
 *                 "soundstr": "铁血加特林", // 音频标题
 *                 "user_id": 234, // 用户 ID
 *                 "username": "UP 主的用户名", // 用户昵称
 *                 "video": true, // 是否绑定视频
 *                 "view_count": 233 // 播放量
 *               }
 *             ]
 *           },
 *         }
 *       ],
 *       "pagination": {
 *         "has_more": true, // 是否还有更多数据
 *         "marker": "{ \"display_id\": 1, \"extra_card_idx\": 0 }" // 加载更多标记
 *       }
 *     }
 *   }
 */
func ActionHomeFeed(c *handler.Context) (handler.ActionResponse, string, error) {
	params, err := newParams(c)
	if err != nil {
		return nil, "", err
	}

	if params.marker.UseFallback {
		if params.marker.Page <= 3 {
			logger.WithFields(logger.Fields{
				"user_id": params.c.UserID(),
				"marker":  params.marker,
			}).Info("HomeFeed 前页已使用兜底数据")
		}
		response, err := params.processFeedFallback()
		if err != nil {
			return nil, "", actionerrors.ErrServerInternal(err, nil)
		}
		return response, "", nil
	}

	feeds, err := requestAlgorithms(params)
	if err != nil {
		response, err := params.processFeedError(err)
		if err != nil {
			return nil, "", actionerrors.ErrServerInternal(err, nil)
		}
		return response, "", nil
	}

	response, err := params.processFeedSuccess(feeds)
	if err != nil {
		return nil, "", actionerrors.ErrServerInternal(err, nil)
	}
	return response, "", nil
}

func (p *params) isFirstRequest() bool {
	return p.ResetMarker != "" || p.Marker == ""
}

func (p *params) processFeedError(err error) (*response, error) {
	if p.isFirstRequest() {
		logger.WithFields(logger.Fields{
			"user_id": p.c.UserID(),
			"error":   err,
		}).Info("HomeFeed 请求算法推荐失败，使用兜底数据")
		return p.processFeedFallback()
	}
	return nil, err
}

func (p *params) processFeedSuccess(feeds *tianma.RecommendResult) (*response, error) {
	switch feeds.Code {
	case tianma.CodeOK:
		return p.processFeedCards(feeds)
	case tianma.CodeItemReqCntNotEnough:
		if p.isFirstRequest() {
			logger.WithFields(logger.Fields{
				"user_id": p.c.UserID(),
				"feeds":   feeds,
			}).Info("HomeFeed 算法推荐数据不足，使用兜底数据")
			return p.processFeedFallback()
		}
		return p.processFeedCards(feeds)
	case tianma.CodeNoResult:
		if p.isFirstRequest() {
			logger.WithFields(logger.Fields{
				"user_id": p.c.UserID(),
				"feeds":   feeds,
			}).Info("HomeFeed 算法推荐无结果，使用兜底数据")
			return p.processFeedFallback()
		}
		result := &response{
			Cards:      make([]*feedCard, 0),
			Pagination: pagination{HasMore: false, Marker: ""},
		}
		return result, nil
	default:
		return p.processFeedError(fmt.Errorf("request tianma failed: %d", feeds.Code))
	}
}

func (p *params) extractIDs(feeds *tianma.RecommendResult) (dramaSoundIDs []int64, liveRoomIDs []int64) {
	dramaSoundIDs = make([]int64, 0, len(feeds.Data))
	liveRoomIDs = make([]int64, 0, len(feeds.Data))

	unsupportedItems := make([]struct {
		Goto string
		ID   int64
	}, 0)

	for _, item := range feeds.Data {
		switch {
		case item.Goto.IsSound():
			dramaSoundIDs = append(dramaSoundIDs, item.ID)
		case item.Goto.IsLive():
			liveRoomIDs = append(liveRoomIDs, item.ID)
		default:
			unsupportedItems = append(unsupportedItems, struct {
				Goto string
				ID   int64
			}{
				Goto: string(item.Goto),
				ID:   item.ID,
			})
		}
	}

	if len(unsupportedItems) > 0 {
		logger.WithFields(logger.Fields{
			"user_id": p.c.UserID(),
			"items":   unsupportedItems,
		}).Warn("获取到不支持的卡片类型")
	}

	return
}

func (p *params) getCardData(dramaSoundIDs []int64, liveRoomIDs []int64) (dramaCardMap map[int64]*DramaCardInfo, liveCardMap map[int64]*liveCardInfo, err error) {
	// 获取广播剧卡片信息
	dramaCardMap, err = p.getDramaCardMap(dramaSoundIDs)
	if err != nil {
		return nil, nil, err
	}

	// 获取直播卡片信息
	liveCardMap, err = p.getLiveCardMap(liveRoomIDs)
	if err != nil {
		return nil, nil, err
	}

	return
}

func (p *params) processNormalCards(feeds *tianma.RecommendResult, dramaCardMap map[int64]*DramaCardInfo, liveCardMap map[int64]*liveCardInfo) []*feedCard {
	normalCards := make([]*feedCard, 0, len(feeds.Data))
	for i, item := range feeds.Data {
		var card *feedCard
		switch {
		case item.Goto.IsSound():
			card = p.createDramaCard(item, dramaCardMap)
		case item.Goto.IsLive():
			card = p.createLiveCard(item, liveCardMap)
		}

		if card != nil {
			// 设置 exposure log 需要的字段
			card.ID = item.ID
			card.Goto = item.Goto
			card.Pos = i + 1
			card.Source = item.Source
			card.AvFeature = item.AVFeature
			card.UserFeature = feeds.UserFeature
			if card.Goto.IsOP() {
				card.Attr = recommendAttrOpConfig
			} else {
				card.Attr = recommendAttrTianma
			}
			card.generateTrace(p)
			normalCards = append(normalCards, card)
		}
	}
	return normalCards
}

func (p *params) createDramaCard(item tianma.RecommendItem, dramaCardMap map[int64]*DramaCardInfo) *feedCard {
	dramaCard, ok := dramaCardMap[item.ID]
	if !ok {
		return nil
	}

	// 创建标签数组
	labels := make([]string, 0, len(dramaCard.Tags))
	for _, tag := range dramaCard.Tags {
		labels = append(labels, fmt.Sprintf("%d:%d:%s", tag.Type, tag.ID, tag.Text))
	}

	// 获取角标名称
	var cornerMark string
	if dramaCard.CornerMark != nil {
		cornerMark = dramaCard.CornerMark.Text
	}

	card := &feedCard{
		CardType:       cardTypeDrama,
		CardData:       dramaCard,
		DislikeReasons: createDramaDislikeReasons(dramaCard),
		TrackID:        item.TrackID,
		Labels:         labels,
		CornerMark:     cornerMark,
	}
	return card
}

func createDramaDislikeReasons(dramaCard *DramaCardInfo) []dislikeReason {
	reasons := []dislikeReason{
		{
			Text:   "不喜欢该剧集",
			Reason: "{}",
		},
	}

	if len(dramaCard.Cvs) > 0 {
		for _, cv := range dramaCard.Cvs {
			reasons = append(reasons, dislikeReason{
				Text:   fmt.Sprintf("不看：%s", cv),
				Reason: "{}",
			})
		}
	}

	reasons = append(reasons, dislikeReason{
		Text:   "重复推荐",
		Reason: "{}",
	})

	return reasons
}

func (p *params) createLiveCard(item tianma.RecommendItem, liveCardMap map[int64]*liveCardInfo) *feedCard {
	liveCard, ok := liveCardMap[item.ID]
	if !ok {
		return nil
	}

	// 创建标签数组
	labels := make([]string, 0, len(liveCard.Tags))
	for _, tag := range liveCard.Tags {
		labels = append(labels, fmt.Sprintf("%d:%d:%s", tag.Type, tag.ID, tag.Text))
	}

	// 获取补充信息文案
	var previewIntroTitle string
	if liveCard.ExtraInfo != nil {
		previewIntroTitle = liveCard.ExtraInfo.Text
	}

	card := &feedCard{
		CardType:          cardTypeLive,
		CardData:          liveCard,
		DislikeReasons:    createLiveDislikeReasons(),
		TrackID:           item.TrackID,
		Labels:            labels,
		PreviewIntroTitle: previewIntroTitle,
	}
	return card
}

func createLiveDislikeReasons() []dislikeReason {
	return []dislikeReason{
		{
			Text:   "直播推荐过多",
			Reason: "{}",
		},
		{
			Text:   "不喜欢该主播",
			Reason: "{}",
		},
	}
}

type ExtraCard struct {
	Position int64
	Card     *feedCard
}

func (p *params) processExtraCards(feeds *tianma.RecommendResult) []*ExtraCard {
	extraCards := make([]*ExtraCard, 0, len(feeds.ExtraCards))

	for _, extraCard := range feeds.ExtraCards {
		// 尝试获取有效的异形卡
		card := p.getNextAvailableExtraCard()
		if card == nil {
			break
		}

		// 设置 exposure log 需要的字段
		card.AvFeature = `{"IsSpecialCard":true}`

		extraCards = append(extraCards, &ExtraCard{
			Position: extraCard.Position,
			Card:     card,
		})
	}

	sort.Slice(extraCards, func(i, j int) bool {
		return extraCards[i].Position < extraCards[j].Position
	})

	return extraCards
}

func (p *params) buildExtraBannerCard() (*feedCard, error) {
	if p.marker.ExtraBannerIDX >= len(ExtraBannerModuleIDs) {
		return nil, nil
	}
	defer func() {
		// 副通栏不需要连续配置，即存在只配置中间某几个副通栏的情况，例如只配置 2 和 3 号副通栏
		// 因此需要每次递增，遇到中间有未配置的副通栏时，直接返回 nil 跳过即可
		p.marker.ExtraBannerIDX++
	}()

	provider := &ExtraBannerProvider{
		NowTime:     goutil.TimeNow(),
		UserID:      p.c.UserID(),
		UserContext: p.c.UserContext(),
		Equipment:   p.c.Equip(),
	}

	moduleID := ExtraBannerModuleIDs[p.marker.ExtraBannerIDX]
	banner, err := provider.GetBanner(moduleID)
	if err != nil {
		return nil, fmt.Errorf("获取副通栏信息失败: %w", err)
	}

	if banner == nil {
		return nil, nil
	}

	card := &feedCard{
		CardType: cardTypeExtraBanner,
		CardData: map[string]any{
			"module_id":    moduleID,
			"extra_banner": banner,
		},
	}

	return card, nil
}

func (p *params) buildRanksCard() (*feedCard, error) {
	favorsResp, err := p.getFavorsInfo()
	if err != nil {
		return nil, fmt.Errorf("获取排行榜信息失败: %w", err)
	}

	if favorsResp == nil || favorsResp.Ranks == nil {
		return nil, nil
	}

	card := &feedCard{
		CardType: cardTypeRanks,
		CardData: favorsResp.Ranks,
	}

	return card, nil
}

func (p *params) buildLiveNowCard() (*feedCard, error) {
	liveModule, err := mpersonamoduleelement.GetLivingModule(p.PersonaID)
	if err != nil {
		return nil, err
	}

	if liveModule == nil {
		return nil, nil
	}

	rooms, err := liveapi.GetRecommendedHomepage(p.c.UserContext())
	if err != nil {
		return nil, fmt.Errorf("获取正在直播异形卡数据失败: %w", err)
	}

	if len(rooms) == 0 {
		return nil, nil
	}

	card := &feedCard{
		CardType: cardTypeLiveNow,
		CardData: rooms,
	}

	return card, nil
}

func (p *params) buildCustomModuleCard() (*feedCard, error) {
	favorsResp, err := p.getFavorsInfo()
	if err != nil {
		return nil, fmt.Errorf("获取自定义模块信息失败: %w", err)
	}

	if favorsResp == nil || len(favorsResp.Modules) <= p.marker.CustomCardIDX {
		return nil, nil
	}

	card := &feedCard{
		CardType: cardTypeCustomModule,
		CardData: favorsResp.Modules[p.marker.CustomCardIDX],
	}

	p.marker.CustomCardIDX++
	return card, nil
}

func (p *params) processFeedCards(feeds *tianma.RecommendResult) (*response, error) {
	dramaSoundIDs, liveRoomIDs := p.extractIDs(feeds)

	dramaCardMap, liveCardMap, err := p.getCardData(dramaSoundIDs, liveRoomIDs)
	if err != nil {
		return nil, err
	}

	normalCards := p.processNormalCards(feeds, dramaCardMap, liveCardMap)
	extraCards := p.processExtraCards(feeds)
	feedCards := arrangeFeedCards(normalCards, extraCards)

	exposureLogSender{
		Context:     p.c,
		Network:     p.Network,
		RefreshType: p.RefreshType,
		RefreshNum:  p.marker.RefreshNum,
		Cards:       feedCards,
		UserFeature: feeds.UserFeature,
	}.send()

	hasMore, marker, err := p.generatePagination(feeds)
	if err != nil {
		return nil, err
	}
	result := &response{
		Cards: feedCards,
		Pagination: pagination{
			HasMore: hasMore,
			Marker:  marker,
		},
	}

	return result, nil
}

func arrangeFeedCards(normalCards []*feedCard, extraCards []*ExtraCard) []*feedCard {
	feedCards := make([]*feedCard, 0, len(normalCards)+len(extraCards))
	feedCards = append(feedCards, normalCards...)

	for _, extraCard := range extraCards {
		position := extraCard.Position
		if position < 0 {
			continue
		}
		if position >= int64(len(feedCards)) {
			feedCards = append(feedCards, extraCard.Card)
		} else {
			newCards := make([]*feedCard, 0, len(feedCards)+1)
			newCards = append(newCards, feedCards[:position]...)
			newCards = append(newCards, extraCard.Card)
			newCards = append(newCards, feedCards[position:]...)
			feedCards = newCards
		}
	}

	for i, card := range feedCards {
		card.Pos = i + 1
	}

	return feedCards
}

func (p *params) getDramaCardMap(dramaSoundIDs []int64) (map[int64]*DramaCardInfo, error) {
	if len(dramaSoundIDs) == 0 {
		return nil, nil
	}

	userID := p.c.UserID()
	return getSoundIDDramaCardMap(dramaSoundIDs, userID)
}

func getSoundIDDramaCardMap(soundIDs []int64, userID int64) (map[int64]*DramaCardInfo, error) {
	param := &dramaCardParams{
		SoundIDs: soundIDs,
		UserID:   userID,
	}
	return param.getSoundIDDramaCardMap()
}

func (p *params) getLiveCardMap(liveRoomIDs []int64) (map[int64]*liveCardInfo, error) {
	if len(liveRoomIDs) == 0 {
		return nil, nil
	}

	userID := p.c.UserID()

	resp, err := liveapi.GetRecommendedLiveCard(p.c.UserContext(), struct {
		UserID  int64   `json:"user_id"`
		RoomIDs []int64 `json:"room_ids"`
	}{
		UserID:  userID,
		RoomIDs: liveRoomIDs,
	})
	if err != nil {
		err := fmt.Errorf("获取直播卡片信息失败: %w", err)
		return nil, err
	}

	liveCardMap := make(map[int64]*liveCardInfo, len(resp.Cards))
	for _, card := range resp.Cards {
		liveCardMap[card.RoomID] = newLiveCardInfo(card)
	}

	return liveCardMap, nil
}

func (p *params) processFeedFallback() (*response, error) {
	// 获取兜底剧集 ID 列表
	allDramaIDs, err := GetFallbackDramas()
	if err != nil {
		return nil, err
	}

	if len(allDramaIDs) == 0 {
		return nil, fmt.Errorf("兜底剧集 ID 列表为空")
	}

	// 确定当前分页的起始偏移量
	offset := 0
	if p.marker.UseFallback {
		offset = p.marker.FallbackOffset
	}

	// 如果偏移量超出了数组长度，返回无更多数据
	if offset >= len(allDramaIDs) {
		result := &response{
			Cards: []*feedCard{},
		}
		result.Pagination.HasMore = false
		result.Pagination.Marker = ""
		return result, nil
	}

	// 计算本次需要返回的剧集 ID 范围
	endOffset := min(offset+requestCount, len(allDramaIDs))
	currentDramaIDs := allDramaIDs[offset:endOffset]

	// 获取剧集首集对应的音频 ID
	dramaIDSoundIDMap, err := GetDramaFirstSoundIDs(currentDramaIDs)
	if err != nil {
		return nil, err
	}

	// 构建音频 ID 列表
	soundIDs := make([]int64, 0, len(dramaIDSoundIDMap))
	for _, dramaID := range currentDramaIDs {
		if soundID, ok := dramaIDSoundIDMap[dramaID]; ok {
			soundIDs = append(soundIDs, soundID)
		}
	}

	// 获取剧集卡片信息
	userID := p.c.UserID()
	dramaCardMap, err := getSoundIDDramaCardMap(soundIDs, userID)
	if err != nil {
		return nil, err
	}

	// 组装 feeds 数据
	result := &response{
		Cards: make([]*feedCard, 0, len(dramaCardMap)),
	}

	for i, soundID := range soundIDs {
		if dramaCard, ok := dramaCardMap[soundID]; ok {
			// 创建标签数组
			labels := make([]string, 0, len(dramaCard.Tags))
			for _, tag := range dramaCard.Tags {
				// 格式为 "label_type:label_id:label_name"，暂时只填写 label_name
				labels = append(labels, "::"+tag.Text)
			}

			// 获取角标名称
			var cornerMark string
			if dramaCard.CornerMark != nil {
				cornerMark = dramaCard.CornerMark.Text
			}

			card := &feedCard{
				CardType:       cardTypeDrama,
				CardData:       dramaCard,
				DislikeReasons: createDramaDislikeReasons(dramaCard),
				TrackID:        "", // Fallback 数据没有 track_id
				ID:             dramaCard.SoundID,
				Goto:           tianma.GotoSound,
				Pos:            i + 1,
				Source:         "", // Fallback 数据没有来源
				AvFeature:      "", // Fallback 数据没有卡片特征
				UserFeature:    "", // Fallback 数据没有用户特征
				Attr:           recommendAttrSupplement,
				Labels:         labels,
				CornerMark:     cornerMark,
			}
			card.generateTrace(p)
			result.Cards = append(result.Cards, card)
		}
	}

	// 上报 exposure log
	exposureLogSender{
		Context:     p.c,
		Network:     p.Network,
		RefreshType: p.RefreshType,
		RefreshNum:  p.marker.RefreshNum,
		Cards:       result.Cards,
		UserFeature: "", // 兜底数据没有用户特征
	}.send()

	// 设置分页信息
	hasMore := endOffset < len(allDramaIDs)
	result.Pagination.HasMore = hasMore

	if hasMore {
		// 更新兜底数据分页状态
		nextMarker := p.marker
		nextMarker.UseFallback = true
		nextMarker.FallbackOffset = endOffset
		markerJSON, jsonErr := json.Marshal(nextMarker)
		if jsonErr != nil {
			err := fmt.Errorf("序列化 marker 失败: %w", jsonErr)
			return nil, err
		}
		result.Pagination.Marker = string(markerJSON)
	} else {
		result.Pagination.Marker = ""
	}

	return result, nil
}

func (p *params) getFavorsInfo() (*appapi.GetMyFavorsResponse, error) {
	resp, ok, err := p.getFavorsInfoFromCache()
	if err != nil {
		return nil, err
	}
	if ok {
		return resp, nil
	}

	resp, err = p.getFavorsInfoFromAPI()
	if err != nil {
		return nil, err
	}

	if err := p.setFavorsInfoToCache(resp); err != nil {
		logger.WithFields(logger.Fields{
			"user_id": p.c.UserID(),
			"error":   err,
		}).Error("将推荐模块信息写入缓存失败")
		// PASS
	}

	return resp, nil
}

func (p *params) getFavorsInfoFromCache() (*appapi.GetMyFavorsResponse, bool, error) {
	key := p.keyFavorsInfo()
	respJson, err := service.LRURedis.Get(key).Result()
	if err != nil {
		if serviceredis.IsRedisNil(err) {
			return nil, false, nil
		}
		return nil, false, err
	}

	var resp appapi.GetMyFavorsResponse
	err = json.Unmarshal([]byte(respJson), &resp)
	if err != nil {
		return nil, false, err
	}
	return &resp, true, nil
}

func (p *params) getFavorsInfoFromAPI() (*appapi.GetMyFavorsResponse, error) {
	return appapi.GetMyFavors(p.c.UserContext(), appapi.GetMyFavorsParam{
		UserID:    p.c.UserID(),
		PersonaID: p.PersonaID,
		IsAll:     appapi.GetMyFavorsIsAllFalse,
	})
}

func (p *params) setFavorsInfoToCache(resp *appapi.GetMyFavorsResponse) error {
	respBytes, err := json.Marshal(resp)
	if err != nil {
		return err
	}
	key := p.keyFavorsInfo()
	// 设置 10 分钟的过期时间
	expiration := 10 * time.Minute
	return service.LRURedis.Set(key, respBytes, expiration).Err()
}

func (p *params) keyFavorsInfo() string {
	return keys.KeyAppHomepageFavors1.Format(p.c.UserID())
}

type params struct {
	PersonaID   int64            `form:"persona_id"`
	RefreshType tianma.FreshType `form:"refresh_type"`
	FeedType    int              `form:"feed_type"`
	Network     int              `form:"network"`
	Marker      string           `form:"marker"`
	ResetMarker string           `form:"reset_marker"`
	marker      *marker

	c             *handler.Context
	getIPInfoFunc func(goutil.UserContext, string, ...goclient.GetIPOptions) (goclient.IPInfo, error)
	recommender   recommender
	timeout       time.Duration
}

func newMarker(resetMarkerStr, markerStr string) (*marker, error) {
	// 默认初始 marker
	initialMarker := marker{
		Page:             1,     // 默认从 1 开始
		RefreshNum:       1,     // 默认从 1 开始
		CustomCardIDX:    0,     // 默认从 0 开始
		ExtraBannerIDX:   0,     // 默认从 0 开始
		ExtraCardTypeIdx: 0,     // 默认从 0 开始
		UseFallback:      false, // 默认不使用兜底数据
		FallbackOffset:   0,     // 默认兜底数据偏移为 0
	}

	// 如果没有提供任何 marker 参数，直接返回初始 marker
	if resetMarkerStr == "" && markerStr == "" {
		return &initialMarker, nil
	}

	// 确定当前使用的 marker 字符串
	currentMarkerStr := resetMarkerStr
	if currentMarkerStr == "" {
		currentMarkerStr = markerStr
	}

	// 解析前一个 marker
	var prevMarker marker
	if err := json.Unmarshal([]byte(currentMarkerStr), &prevMarker); err != nil {
		return nil, actionerrors.ErrParams
	}

	// 如果是重置加载（提供了 resetMarkerStr）
	if resetMarkerStr != "" {
		// 不管之前是什么类型的数据，重置加载时都使用初始 marker 但继承刷新次数加 1
		initialMarker.RefreshNum = prevMarker.RefreshNum + 1
		return &initialMarker, nil
	}

	nextMarker := prevMarker
	nextMarker.Page++
	nextMarker.RefreshNum++
	return &nextMarker, nil
}

func newParams(c *handler.Context) (*params, error) {
	var params params
	if err := c.Bind(&params); err != nil {
		return nil, actionerrors.ErrParams
	}

	markerObj, err := newMarker(params.ResetMarker, params.Marker)
	if err != nil {
		return nil, err
	}
	params.marker = markerObj

	// 验证参数
	if params.RefreshType < 0 || params.FeedType < 0 {
		return nil, actionerrors.ErrParams
	}

	if params.marker != nil {
		if params.marker.RefreshNum <= 0 || params.marker.CustomCardIDX < 0 || params.marker.ExtraBannerIDX < 0 || params.marker.ExtraCardTypeIdx < 0 {
			return nil, actionerrors.ErrParams
		}
		if params.marker.UseFallback && params.marker.FallbackOffset < 0 {
			return nil, actionerrors.ErrParams
		}
	}

	// 如果未指定画像 ID，则默认使用普通女画像
	if params.PersonaID == 0 {
		params.PersonaID = persona.TypeGirl
	}

	params.c = c
	params.getIPInfoFunc = goclient.GetIPInfo
	params.recommender = GetRecommenderForEnv()
	params.timeout = params.recommender.Timeout()

	return &params, nil
}

func (p *params) getIPInfo() goclient.IPInfo {
	equip := p.c.Equip()
	sm := goutil.SmartUserContext{
		UID:         p.c.UserID(),
		IP:          p.c.ClientIP(),
		UserToken:   p.c.Token(),
		UserEquipID: equip.EquipID,
		UserBUVID:   equip.BUVID,
		UA:          p.c.UserAgent(),
		Req:         p.c.Request(),
	}
	info, err := p.getIPInfoFunc(sm, p.c.ClientIP())
	if err != nil {
		logger.WithFields(logger.Fields{"ip": p.c.ClientIP(), "user_id": p.c.UserID()}).Errorf("获取用户地理位置信息失败: %v", err)
		// PASS
	}
	return info
}

const (
	NetworkUnknown int = iota
	NetworkWifi
	NetworkCellular
	NetworkOffline
	NetworkOther
)

func (p *params) getTianmaNetwork() tianma.Network {
	switch p.Network {
	case NetworkWifi:
		return tianma.NetworkWifi
	case NetworkCellular:
		return tianma.NetworkCellular
	case NetworkOther:
		return tianma.NetworkOtherNet
	case NetworkOffline:
		fallthrough
	default:
		return tianma.UnknownStringValue
	}
}

func (p *params) getTianmaSex() tianma.Sex {
	var sex tianma.Sex
	switch user.GetUserSex(p.c.UserID()) {
	case user.SexUnknown:
		sex = tianma.UnknownIntValue
	case user.SexBoy:
		sex = tianma.SexMale
	case user.SexGirl:
		sex = tianma.SexFemale
	default:
		sex = tianma.UnknownIntValue
	}
	return sex
}

func (p *params) getChannel() string {
	return p.c.Request().Header.Get("channel")
}

func (p *params) getDeviceModel() string {
	return p.c.Equip().DeviceModel
}

func (p *params) getPlatform() tianma.Platform {
	os := p.c.Equip().OS
	switch os {
	case goutil.Android:
		return tianma.PlatformAndroid
	case goutil.IOS:
		return tianma.PlatformIOS
	case goutil.Web:
		return tianma.PlatformWeb
	case goutil.MobileWeb:
		return tianma.PlatformMobileWeb
	case goutil.Windows:
		return tianma.PlatformWindows
	case goutil.HarmonyOS:
		return tianma.PlatformHarmonyOS
	default:
		return tianma.UnknownStringValue
	}
}

func (p *params) getAppVersion() string {
	return p.c.Equip().AppVersion
}

func (p *params) generatePagination(feeds *tianma.RecommendResult) (hasMore bool, marker string, err error) {
	hasMore = feeds.Code == tianma.CodeOK
	if hasMore {
		if p.marker == nil {
			err := fmt.Errorf("marker 为 nil")
			return false, "", err
		}

		markerJSON, jsonErr := json.Marshal(p.marker)
		if jsonErr != nil {
			err := fmt.Errorf("序列化 marker 失败: %w", jsonErr)
			return false, "", err
		}
		marker = string(markerJSON)
	}
	return
}

type marker struct {
	Page             int64 `json:"page"`                      // 当前页码
	RefreshNum       int64 `json:"refresh_num"`               // 刷新次数
	CustomCardIDX    int   `json:"custom_card_idx"`           // 当前使用到的自定义卡片下标
	ExtraBannerIDX   int   `json:"extra_banner_idx"`          // 当前使用到的副通栏下标
	ExtraCardTypeIdx int   `json:"extra_card_type_idx"`       // 当前使用到的异形卡类型序号
	UseFallback      bool  `json:"use_fallback,omitempty"`    // 是否使用兜底数据
	FallbackOffset   int   `json:"fallback_offset,omitempty"` // 兜底数据分页偏移
}

type pagination struct {
	HasMore bool   `json:"has_more"` // 是否还有更多数据
	Marker  string `json:"marker"`   // 加载更多标记
}

type response struct {
	Cards      []*feedCard `json:"cards"`
	Pagination pagination  `json:"pagination"`
}

const requestCount = 10

func requestAlgorithms(params *params) (*tianma.RecommendResult, error) {
	ipInfo := params.getIPInfo()
	userID := params.c.UserID()
	var ctime int64
	if userID != 0 {
		var err error
		ctime, err = user.MowangskUser{}.GetCTimeByID(userID)
		if err != nil {
			logger.WithField("user_id", userID).Errorf("GetCTimeByID failed, error: %v", err)
			// PASS
		}
	}
	algorithmParams := tianma.RecommendParams{
		// 必填字段
		Cmd:        tianma.RecommendCmdHomeFeeds,  // 首页 Feed 流
		MID:        userID,                        // 用户 ID
		Buvid:      params.c.BUVID(),              // 设备唯一标识
		RequestCnt: requestCount,                  // 请求数量
		Timeout:    params.timeout.Milliseconds(), // 请求超时，单位：毫秒
		DisplayID:  params.marker.RefreshNum,      // 从 marker 中获取 display_id
		FreshType:  params.RefreshType,            // 根据 refresh_type 获取刷新类型
		Network:    params.getTianmaNetwork(),     // 获取网络类型
		Country:    ipInfo.CountryName,            // 国家，默认中国
		Sex:        params.getTianmaSex(),         // 用户性别
		Persona:    params.PersonaID,              // 用户画像
		TS:         goutil.TimeNow().Unix(),       // 当前时间戳
		Chid:       params.getChannel(),           // 获取渠道标识
		Model:      params.getDeviceModel(),       // 设备型号
		Platform:   params.getPlatform(),          // 设备平台
		Version:    params.getAppVersion(),        // 应用版本

		// 可选字段
		Province:       ipInfo.RegionName,  // 省份，可选
		City:           ipInfo.CityName,    // 城市，可选
		Page:           params.marker.Page, // 当前页码
		FirstLoginTime: ctime,              // 首次登录时间戳，秒级
	}
	userConfig, err := user.GetUserConfig(userID, params.c.BUVID(), ctime)
	if err != nil {
		logger.WithField("user_id", userID).Errorf("GetUserConfig failed, error: %v", err)
		// PASS
	}
	if userConfig != nil && *userConfig.AppConfig.PersonalizedRecommend == user.ConfigDisable {
		algorithmParams.ClosePersonalizedRecommend = tianma.PersonalizedClose
	}
	return params.recommender.Recommend(algorithmParams)
}

func (c *feedCard) generateTrace(p *params) {
	// 创建 trace 对象
	trace := map[string]interface{}{
		"feed_type":    p.FeedType,
		"refresh_num":  p.marker.RefreshNum,
		"refresh_type": p.RefreshType,
		"attr":         c.Attr,
		"track_id":     c.TrackID,
		"goto":         c.Goto,
		"av_feature":   c.AvFeature,
		"user_feature": c.UserFeature,
	}

	// 添加标签信息
	if len(c.Labels) > 0 {
		trace["labels"] = c.Labels
	}

	// 根据卡片类型添加不同的信息
	switch c.CardType {
	case cardTypeDrama:
		// 剧集卡片特有字段
		if c.CornerMark != "" {
			trace["corner_mark"] = c.CornerMark
		}
	case cardTypeLive:
		// 直播卡片特有字段
		if c.PreviewIntroTitle != "" {
			trace["preview_intro_title"] = c.PreviewIntroTitle
		}
	}

	// 转换为 JSON 字符串
	jsonBytes, err := json.Marshal(trace)
	if err != nil {
		logger.Error(err)
		return
	}

	c.Trace = string(jsonBytes)
}

type extraCardTypeInfo struct {
	CardType cardType                         // 卡片类型
	Build    func(*params) (*feedCard, error) // 创建该类型卡片的函数
}

var extraCardTypes = []extraCardTypeInfo{
	{cardTypeExtraBanner, (*params).buildExtraBannerCard},   // 序号 0: 轮播通栏
	{cardTypeRanks, (*params).buildRanksCard},               // 序号 1: 排行榜
	{cardTypeExtraBanner, (*params).buildExtraBannerCard},   // 序号 2: 轮播通栏
	{cardTypeExtraBanner, (*params).buildExtraBannerCard},   // 序号 3: 轮播通栏
	{cardTypeLiveNow, (*params).buildLiveNowCard},           // 序号 4: 正在直播模块
	{cardTypeExtraBanner, (*params).buildExtraBannerCard},   // 序号 5: 轮播通栏
	{cardTypeCustomModule, (*params).buildCustomModuleCard}, // 序号 6: 自定义模块
}

func (p *params) getNextExtraCard() (extraCardTypeInfo, bool) {
	if len(extraCardTypes) == 0 {
		return extraCardTypeInfo{}, false
	}
	if p.marker.ExtraCardTypeIdx >= len(extraCardTypes)-1 {
		// 最后一个自定义模块类型比较特殊，需要支持配置任意数量的自定义模块
		// 所以在命中最后一个 builder 的时候直接返回，不对 index 递增
		// 这样可以反复调用 buildCustomModuleCard 来获取所有配置的模块
		return extraCardTypes[len(extraCardTypes)-1], false
	}
	cardTypeInfo := extraCardTypes[p.marker.ExtraCardTypeIdx]
	p.marker.ExtraCardTypeIdx++
	return cardTypeInfo, true
}

func (p *params) getNextAvailableExtraCard() *feedCard {
	for {
		cardTypeInfo, hasNext := p.getNextExtraCard()
		card, err := cardTypeInfo.Build(p)

		if err != nil {
			logger.WithFields(logger.Fields{
				"user_id":   p.c.UserID(),
				"card_type": cardTypeInfo.CardType,
				"error":     err,
			}).Error("获取异形卡数据失败")
			if !hasNext {
				break
			}
			// 当前异形卡获取失败，继续尝试下一个
			continue
		}

		// 如果卡片为 nil（例如没有配置数据），继续尝试下一个异形卡
		if card == nil {
			if !hasNext {
				break
			}
			// 当前异形卡获取失败，继续尝试下一个
			continue
		}

		// 获取到有效卡片，返回
		return card
	}

	// 所有类型都尝试失败，返回 nil
	return nil
}
