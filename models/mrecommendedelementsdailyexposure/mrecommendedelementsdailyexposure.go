package mrecommendedelementsdailyexposure

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-main/service"
)

// 场景，1：首页，2：直播页
const (
	SceneHomePage = iota + 1 // 首页
	SceneLivePage            // 直播页
)

// Model 推荐位干预卡每日曝光量
type Model struct {
	ID            int64  `gorm:"column:id;primary_key"` // 主键
	CreateTime    int64  `gorm:"column:create_time"`    // 创建时间
	ModifiedTime  int64  `gorm:"column:modified_time"`  // 更新时间
	Bizdate       string `gorm:"column:bizdate"`        // 业务日期（格式：2023-03-01）
	Scene         int    `gorm:"column:scene"`          // 场景，1：首页，2：直播页
	ElementID     int64  `gorm:"column:element_id" `    // 干预卡 ID，sence=1 时为 m_recommended_elements id，sence=2 时为 live_recommended_elements id
	ExposureCount int64  `gorm:"column:exposure_count"` // 当日曝光量
}

// TableName for current model
func (Model) TableName() string {
	return "m_recommended_elements_daily_exposure"
}

// DB the db instance of RecommendedElementsDailyExposure model
func (m Model) DB() *gorm.DB {
	return service.DB.Table(m.TableName())
}

// BeforeCreate automatically sets columns create_time and modified_time
func (m *Model) BeforeCreate() (err error) {
	now := goutil.TimeNow().Unix()
	m.CreateTime = now
	m.ModifiedTime = now

	return nil
}

// UpdateOrCreateDailyExposure 更新或创建推荐位干预卡每日曝光量
func UpdateOrCreateDailyExposure(elementIDs []int64, scene int) {
	elementIDsLen := len(elementIDs)
	if elementIDsLen == 0 {
		return
	}

	todayBizdate := goutil.TimeNow().Format("2006-01-02")
	// 查询今天已存在的记录
	var existingModels []Model
	err := Model{}.DB().Table(Model{}.TableName()).
		Where("bizdate = ? AND scene = ? AND element_id IN (?)", todayBizdate, scene, elementIDs).
		Find(&existingModels).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		logger.Errorf("查询推荐位干预卡每日（实时）曝光量失败: %v", err)
		// PASS
	}

	// 分离需要更新和插入的ID
	updateElementIDs := make([]int64, 0, elementIDsLen)
	insertElementIDs := make([]int64, 0, elementIDsLen)
	if len(existingModels) > 0 {
		// 构建存在 ID 的映射
		existingMap := make(map[int64]struct{})
		for _, model := range existingModels {
			existingMap[model.ElementID] = struct{}{}
		}
		for _, id := range elementIDs {
			if _, exists := existingMap[id]; exists {
				updateElementIDs = append(updateElementIDs, id)
			} else {
				insertElementIDs = append(insertElementIDs, id)
			}
		}
	} else {
		insertElementIDs = elementIDs
	}

	// 插入新记录，并发情况下可能会触发唯一索引错误，触发唯一索引错误时说明记录已存在，需要降级为更新记录
	nowUnix := goutil.TimeNow().Unix()
	if len(insertElementIDs) > 0 {
		for _, id := range insertElementIDs {
			err = Model{}.DB().Create(&Model{
				Bizdate:       todayBizdate,
				Scene:         scene,
				ElementID:     id,
				ExposureCount: 1,
			}).Error
			if err != nil {
				if servicedb.IsUniqueError(err) {
					// 触发唯一索引错误时说明记录已存在，走后续的批量更新逻辑
					updateElementIDs = append(updateElementIDs, id)
				} else {
					logger.WithField("element_id", id).Errorf("插入推荐位干预卡每日（实时）曝光量失败: %v", err)
					// PASS
				}
			}
		}
	}

	// 批量更新现有记录
	if len(updateElementIDs) <= 0 {
		return
	}
	err = Model{}.DB().
		Where("bizdate = ? AND scene = ? AND element_id IN (?)", todayBizdate, scene, updateElementIDs).
		Updates(map[string]interface{}{
			"exposure_count": gorm.Expr("exposure_count + 1"),
			"modified_time":  nowUnix,
		}).Error
	if err != nil {
		logger.WithField("element_ids", updateElementIDs).Errorf("更新推荐位干预卡每日曝光量失败: %v", err)
		// PASS
	}
}
