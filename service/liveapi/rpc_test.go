package liveapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-main/service"
)

func TestMain(m *testing.M) {
	service.InitTest()
	m.Run()
}

func TestGetLiveExtraBanners(t *testing.T) {
	tests := []struct {
		name     string
		request  *ExtraBannersParam
		wantErr  bool
		mockResp *ExtraBannersResponse
		validate func(t *testing.T, resp *ExtraBannersResponse)
	}{
		{
			name: "NormalResponse",
			request: &ExtraBannersParam{
				UserID:    1,
				Positions: []int64{1},
			},
			wantErr: false,
			mockResp: &ExtraBannersResponse{
				Data: map[int64][]ExtraBannerItem{
					1: {
						{
							Pic: "https://static-test.maoercdn.com/test/test.png",
							URL: "https://www.uat.missevan.com/live/665152",
						},
					},
				},
			},
			validate: func(t *testing.T, resp *ExtraBannersResponse) {
				assert.NotNil(t, resp)
				assert.NotEmpty(t, resp)
				banners, ok := resp.Data[1]
				assert.True(t, ok)
				assert.NotEmpty(t, banners)
				assert.Equal(t, "https://static-test.maoercdn.com/test/test.png", banners[0].Pic)
				assert.Equal(t, "https://www.uat.missevan.com/live/665152", banners[0].URL)
			},
		},
		{
			name: "EmptyResponse",
			request: &ExtraBannersParam{
				UserID:    1,
				Positions: []int64{},
			},
			wantErr:  false,
			mockResp: &ExtraBannersResponse{},
			validate: func(t *testing.T, resp *ExtraBannersResponse) {
				assert.NotNil(t, resp)
				assert.Empty(t, resp)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cancel := mrpc.SetMock(URIGetExtraBanners, func(input interface{}) (interface{}, error) {
				return tt.mockResp, nil
			})
			defer cancel()

			var ctx mrpc.UserContext
			resp, err := GetLiveExtraBanners(ctx, *tt.request)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			tt.validate(t, resp)
		})
	}
}

func TestGetRecommendedLiveCard(t *testing.T) {
	tests := []struct {
		name    string
		request struct {
			UserID  int64   `json:"user_id"`
			RoomIDs []int64 `json:"room_ids"`
		}
		wantErr  bool
		mockResp RecommendedLiveCardResponse
		validate func(t *testing.T, resp *RecommendedLiveCardResponse)
	}{
		{
			name: "NormalResponse",
			request: struct {
				UserID  int64   `json:"user_id"`
				RoomIDs []int64 `json:"room_ids"`
			}{
				UserID:  123,
				RoomIDs: []int64{108324},
			},
			wantErr: false,
			mockResp: RecommendedLiveCardResponse{
				Cards: []*LiveCard{
					{
						RoomID:          108324,
						Name:            "直播间名称",
						CreatorID:       123213,
						CreatorUsername: "浅声暖唱の龙年大吉",
						CreatorIconURL:  "https://static.maoercdn.com/avatars/202410/07/f70f67dfcb93e4bf70a24584df7cff9d095351.png",
						CoverURL:        "https://static.maoercdn.com/fmcovers/202410/08/435992d3bf5ae1e987eeba63ac00f247.jpg",
						Announcement:    "这是一条直播间公告信息",
						Status: struct {
							Open      int `json:"open"`
							RedPacket int `json:"red_packet"`
							LuckyBag  int `json:"lucky_bag"`
						}{
							Open:      1,
							RedPacket: 1,
							LuckyBag:  1,
						},
						Statistics: &struct {
							Score int64 `json:"score"`
						}{
							Score: 421314,
						},
						ExtraInfo: &struct {
							IconURL string `json:"icon_url"`
							Title   string `json:"title"`
						}{
							IconURL: "https://static.maoercdn.com/live/labelicon/livelist/lasthour01-1.png",
							Title:   "正在免费抽《广播剧标题》福袋",
						},
						RecommendTag: &Tag{
							Type: 6,
							Text: "最近看过",
						},
						CatalogTag: &Tag{
							Type: 7,
							Text: "音乐",
						},
						CustomTag: &Tag{
							Type: 4,
							Text: "元气少年",
						},
					},
				},
			},
			validate: func(t *testing.T, resp *RecommendedLiveCardResponse) {
				assert.NotNil(t, resp)
				assert.NotEmpty(t, resp.Cards)
				card := resp.Cards[0]
				assert.Equal(t, int64(108324), card.RoomID)
				assert.Equal(t, "直播间名称", card.Name)
				assert.Equal(t, int64(123213), card.CreatorID)
				assert.Equal(t, "浅声暖唱の龙年大吉", card.CreatorUsername)
				assert.Equal(t, 1, card.Status.Open)
				assert.Equal(t, 1, card.Status.RedPacket)
				assert.Equal(t, 1, card.Status.LuckyBag)
				assert.Equal(t, int64(421314), card.Statistics.Score)
				assert.Equal(t, "正在免费抽《广播剧标题》福袋", card.ExtraInfo.Title)
				assert.Equal(t, "最近看过", card.RecommendTag.Text)
				assert.Equal(t, "音乐", card.CatalogTag.Text)
				assert.Equal(t, "元气少年", card.CustomTag.Text)
				assert.Equal(t, "这是一条直播间公告信息", card.Announcement)
			},
		},
		{
			name: "EmptyResponse",
			request: struct {
				UserID  int64   `json:"user_id"`
				RoomIDs []int64 `json:"room_ids"`
			}{
				UserID:  123,
				RoomIDs: []int64{},
			},
			wantErr: false,
			mockResp: RecommendedLiveCardResponse{
				Cards: []*LiveCard{},
			},
			validate: func(t *testing.T, resp *RecommendedLiveCardResponse) {
				assert.NotNil(t, resp)
				assert.Empty(t, resp.Cards)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cancel := mrpc.SetMock(URIGetRecommendedLiveCard, func(input interface{}) (interface{}, error) {
				return tt.mockResp, nil
			})
			defer cancel()

			var ctx mrpc.UserContext
			resp, err := GetRecommendedLiveCard(ctx, tt.request)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			tt.validate(t, resp)
		})
	}
}

func TestGetRecommendedHomepage(t *testing.T) {
	tests := []struct {
		name     string
		wantErr  bool
		mockResp *RecommendedHomepageResponse
		validate func(t *testing.T, resp []*RecommendedHomepageRoom)
	}{
		{
			name:    "NormalResponse",
			wantErr: false,
			mockResp: &RecommendedHomepageResponse{
				Rooms: []*RecommendedHomepageRoom{
					{
						Position:        1,
						RoomID:          22489473,
						Name:            "测试直播间",
						CoverURL:        "https://static.missevan.com/profile/01.png",
						CreatorID:       10,
						CreatorUsername: "测试主播",
						CreatorIconURL:  "https://static.missevan.com/profile/01.png",
						CatalogID:       10,
						CatalogName:     "娱乐",
						CatalogColor:    "#D68DFE",
						CustomTag: struct {
							TagID   int64  `json:"tag_id"`
							TagName string `json:"tag_name"`
						}{
							TagID:   10001,
							TagName: "腹黑青叔",
						},
						Status: struct {
							Open      int `json:"open"`
							PK        int `json:"pk"`
							RedPacket int `json:"red_packet"`
						}{
							Open:      1,
							PK:        1,
							RedPacket: 1,
						},
					},
				},
			},
			validate: func(t *testing.T, resp []*RecommendedHomepageRoom) {
				assert.NotNil(t, resp)
				assert.NotEmpty(t, resp)
				room := resp[0]
				assert.Equal(t, int64(1), room.Position)
				assert.Equal(t, int64(22489473), room.RoomID)
				assert.Equal(t, "测试直播间", room.Name)
				assert.Equal(t, "https://static.missevan.com/profile/01.png", room.CoverURL)
				assert.Equal(t, int64(10), room.CreatorID)
				assert.Equal(t, "测试主播", room.CreatorUsername)
				assert.Equal(t, "https://static.missevan.com/profile/01.png", room.CreatorIconURL)
				assert.Equal(t, int64(10), room.CatalogID)
				assert.Equal(t, "娱乐", room.CatalogName)
				assert.Equal(t, "#D68DFE", room.CatalogColor)
				assert.Equal(t, int64(10001), room.CustomTag.TagID)
				assert.Equal(t, "腹黑青叔", room.CustomTag.TagName)
				assert.Equal(t, 1, room.Status.Open)
				assert.Equal(t, 1, room.Status.PK)
				assert.Equal(t, 1, room.Status.RedPacket)
			},
		},
		{
			name:    "EmptyResponse",
			wantErr: false,
			mockResp: &RecommendedHomepageResponse{
				Rooms: []*RecommendedHomepageRoom{},
			},
			validate: func(t *testing.T, resp []*RecommendedHomepageRoom) {
				assert.NotNil(t, resp)
				assert.Empty(t, resp)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cancel := mrpc.SetMock(URIGetRecommendedHomepage, func(input interface{}) (interface{}, error) {
				return tt.mockResp, nil
			})
			defer cancel()

			var ctx mrpc.UserContext
			resp, err := GetRecommendedHomepage(ctx)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			tt.validate(t, resp)
		})
	}
}
